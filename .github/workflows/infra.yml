name: Deploy Infrastructure with Terraform and IAC Validation

on:
  workflow_dispatch:
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main
    paths-ignore:
      - '**/README.md'
      - '.gitignore'
      - 'gcp_core_modules/**'
    
env:
  TF_VERSION: "1.12.1"
  TF_PLAN_FILE: "tfplan"
  TF_PLAN_JSON_FILE: "tfplan.json"

jobs:
  discover-changed-tf-roots:
    name: Discover Changed Terraform Roots
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
    outputs:
      workdirs_json: ${{ steps.determine_tf_workdirs.outputs.workdirs_json }}
      has_changes: ${{ steps.determine_tf_workdirs.outputs.has_changes }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Get changed tfvars files
        id: changed_tfvars_files
        uses: tj-actions/changed-files@v44
        with:
          files: |
            01_clients/**/**.tfvars
            00_internal/**.tfvars
          files_ignore: |
            gcp_core_modules/**
            01_clients/*/common.tfvars

      - name: Get changed client common tfvars
        id: changed_client_common_tfvars
        uses: tj-actions/changed-files@v44
        with:
          files: |
            01_clients/*/common.tfvars
          files_ignore: |
            gcp_core_modules/**

      - name: Get changed wrapper 00_internal
        id: changed_wrapper_internal
        uses: tj-actions/changed-files@v44
        with:
          files: |
            wrapper_modules/00_internal/**

      - name: Get changed wrapper 01_client
        id: changed_wrapper_client
        uses: tj-actions/changed-files@v44
        with:
          files: |
            wrapper_modules/01_client/**

      - name: Determine Terraform Working Directories
        id: determine_tf_workdirs 
        run: |
          TFVARS_FILES="${{ steps.changed_tfvars_files.outputs.all_changed_and_modified_files }}"
          CLIENT_COMMON_TFVARS="${{ steps.changed_client_common_tfvars.outputs.all_changed_and_modified_files }}"
          WRAPPER_INTERNAL_CHANGED="${{ steps.changed_wrapper_internal.outputs.any_changed }}"
          WRAPPER_CLIENT_CHANGED="${{ steps.changed_wrapper_client.outputs.any_changed }}"
          
          echo "Changed tfvars files: $TFVARS_FILES"
          echo "Changed client common tfvars: $CLIENT_COMMON_TFVARS"
          echo "Wrapper 00_internal changed: $WRAPPER_INTERNAL_CHANGED"
          echo "Wrapper 01_client changed: $WRAPPER_CLIENT_CHANGED"

          WORKDIR_CONFIGS=()

          # If wrapper_modules/00_internal changed, trigger 00_internal/common.tfvars
          if [ "$WRAPPER_INTERNAL_CHANGED" = "true" ]; then
            echo "Wrapper 00_internal changed - triggering 00_internal/common.tfvars"
            
            if [ -f "00_internal/common.tfvars" ]; then
              WORKDIR="wrapper_modules/00_internal"
              BACKEND_PREFIX="terraform/saascada"
              TFVARS_FILE="../../00_internal/common.tfvars"
              COMMON_TFVARS_FILE="../../00_internal/common.tfvars"
              
              CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"\",\"env\":\"\",\"client_dir\":\"\",\"backend_prefix\":\"$BACKEND_PREFIX\",\"tfvars_file\":\"$TFVARS_FILE\",\"common_tfvars_file\":\"$COMMON_TFVARS_FILE\"}"
              WORKDIR_CONFIGS+=("$CONFIG")
              echo "Added internal config (wrapper change): $CONFIG"
            else
              echo "Error: 00_internal/common.tfvars not found"
              exit 1
            fi
          fi

          # If wrapper_modules/01_client changed, find ALL 01_clients tfvars files
          if [ "$WRAPPER_CLIENT_CHANGED" = "true" ]; then
            echo "Wrapper 01_client changed - triggering ALL client environments"
            
            if [ -d "01_clients" ]; then
              ALL_CLIENT_TFVARS=$(find 01_clients -name "*.tfvars" -type f 2>/dev/null | grep -v common.tfvars | sort || true)
              echo "Found client tfvars files: $ALL_CLIENT_TFVARS"
              
              if [ -n "$ALL_CLIENT_TFVARS" ]; then
                for file in $ALL_CLIENT_TFVARS; do
                  echo "Processing file (wrapper 01_client change): $file"
                  
                  if [[ "$file" =~ ^01_clients/([^/]+)/([^/]+)/.*\.tfvars$ ]]; then
                    CLIENT="${BASH_REMATCH[1]}"
                    ENV="${BASH_REMATCH[2]}"
                    CLIENT_DIR="01_clients/${CLIENT}"
                    WORKDIR="wrapper_modules/01_client"
                    BACKEND_PREFIX="terraform/${CLIENT}/${ENV}"
                    TFVARS_FILE="../../${file}"
                    COMMON_TFVARS_FILE="../../${CLIENT_DIR}/common.tfvars"
                    
                    CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"$CLIENT\",\"env\":\"$ENV\",\"client_dir\":\"$CLIENT_DIR\",\"backend_prefix\":\"$BACKEND_PREFIX\",\"tfvars_file\":\"$TFVARS_FILE\",\"common_tfvars_file\":\"$COMMON_TFVARS_FILE\"}"
                    WORKDIR_CONFIGS+=("$CONFIG")
                    echo "Added client config (wrapper change): $CONFIG"
                  fi
                done
              else
                echo "No client tfvars files found in 01_clients directory"
              fi
            else
              echo "Directory 01_clients does not exist"
            fi
          fi

          # If client common.tfvars changed, trigger all environments for that client
          if [ -n "$CLIENT_COMMON_TFVARS" ]; then
            echo "Processing client common.tfvars changes"
            
            for common_file in $CLIENT_COMMON_TFVARS; do
              echo "Processing common.tfvars change: $common_file"
              
              if [[ "$common_file" =~ ^01_clients/([^/]+)/common\.tfvars$ ]]; then
                CLIENT="${BASH_REMATCH[1]}"
                echo "Client $CLIENT common.tfvars changed - triggering all environments for this client"
                
                # Find all environment tfvars for this specific client
                CLIENT_ENV_TFVARS=$(find "01_clients/${CLIENT}" -name "*.tfvars" -type f 2>/dev/null | grep -v common.tfvars | sort || true)
                echo "Found environment tfvars for client $CLIENT: $CLIENT_ENV_TFVARS"
                
                if [ -n "$CLIENT_ENV_TFVARS" ]; then
                  for env_file in $CLIENT_ENV_TFVARS; do
                    echo "Processing environment file (client common change): $env_file"
                    
                    if [[ "$env_file" =~ ^01_clients/([^/]+)/([^/]+)/.*\.tfvars$ ]]; then
                      ENV="${BASH_REMATCH[2]}"
                      CLIENT_DIR="01_clients/${CLIENT}"
                      WORKDIR="wrapper_modules/01_client"
                      BACKEND_PREFIX="terraform/${CLIENT}/${ENV}"
                      TFVARS_FILE="../../${env_file}"
                      COMMON_TFVARS_FILE="../../${CLIENT_DIR}/common.tfvars"
                      
                      CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"$CLIENT\",\"env\":\"$ENV\",\"client_dir\":\"$CLIENT_DIR\",\"backend_prefix\":\"$BACKEND_PREFIX\",\"tfvars_file\":\"$TFVARS_FILE\",\"common_tfvars_file\":\"$COMMON_TFVARS_FILE\"}"
                      WORKDIR_CONFIGS+=("$CONFIG")
                      echo "Added client config (common.tfvars change): $CONFIG"
                    fi
                  done
                else
                  echo "No environment tfvars found for client $CLIENT"
                fi
              fi
            done
          fi

          # Process regular tfvars file changes (if any)
          if [ -n "$TFVARS_FILES" ]; then
            echo "Processing regular tfvars file changes"
            
            for file in $TFVARS_FILES; do
              echo "Processing file (tfvars change): $file"

              # Skip common.tfvars files (they should be handled separately)
              if [[ "$file" =~ common\.tfvars$ ]]; then
                echo "Skipping common.tfvars file: $file (handled separately)"
                continue
              fi

              # Skip if this config already exists (from wrapper module changes or common.tfvars changes)
              ALREADY_EXISTS=false
              for existing_config in "${WORKDIR_CONFIGS[@]}"; do
                if echo "$existing_config" | grep -q "\"tfvars_file\":\"../../${file}\""; then
                  ALREADY_EXISTS=true
                  echo "Config for $file already exists, skipping duplicate"
                  break
                fi
              done
              
              if [ "$ALREADY_EXISTS" = true ]; then
                continue
              fi
              
              if [[ "$file" =~ ^01_clients/([^/]+)/([^/]+)/.*\.tfvars$ ]]; then
                # Only process if wrapper 01_client didn't change
                if [ "$WRAPPER_CLIENT_CHANGED" = "false" ]; then
                  CLIENT="${BASH_REMATCH[1]}"
                  ENV="${BASH_REMATCH[2]}"
                  CLIENT_DIR="01_clients/${CLIENT}"
                  WORKDIR="wrapper_modules/01_client"
                  BACKEND_PREFIX="terraform/${CLIENT}/${ENV}"
                  TFVARS_FILE="../../${file}"
                  COMMON_TFVARS_FILE="../../${CLIENT_DIR}/common.tfvars"
                  
                  CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"$CLIENT\",\"env\":\"$ENV\",\"client_dir\":\"$CLIENT_DIR\",\"backend_prefix\":\"$BACKEND_PREFIX\",\"tfvars_file\":\"$TFVARS_FILE\",\"common_tfvars_file\":\"$COMMON_TFVARS_FILE\"}"
                  WORKDIR_CONFIGS+=("$CONFIG")
                  echo "Added client config (tfvars change): $CONFIG"
                fi
                
              elif [[ "$file" =~ ^00_internal/.*\.tfvars$ ]]; then
                # Only process if wrapper 00_internal didn't change
                if [ "$WRAPPER_INTERNAL_CHANGED" = "false" ]; then
                  WORKDIR="wrapper_modules/00_internal"
                  BACKEND_PREFIX="terraform/saascada"
                  TFVARS_FILE="../../${file}"
                  COMMON_TFVARS_FILE="../../00_internal/common.tfvars"
                  
                  CONFIG="{\"workdir\":\"$WORKDIR\",\"client\":\"\",\"env\":\"\",\"client_dir\":\"\",\"backend_prefix\":\"$BACKEND_PREFIX\",\"tfvars_file\":\"$TFVARS_FILE\",\"common_tfvars_file\":\"$COMMON_TFVARS_FILE\"}"
                  WORKDIR_CONFIGS+=("$CONFIG")
                  echo "Added internal config (tfvars change): $CONFIG"
                fi
              fi
            done
          fi

          # Check if we have any configurations
          if [ ${#WORKDIR_CONFIGS[@]} -eq 0 ]; then
            echo "No relevant changes found."
            echo "workdirs_json=[]" >> $GITHUB_OUTPUT
            echo "has_changes=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Remove duplicates and create final JSON
          UNIQUE_CONFIGS=($(printf "%s\n" "${WORKDIR_CONFIGS[@]}" | sort -u))
          JSON_ARRAY=$(printf "%s\n" "${UNIQUE_CONFIGS[@]}" | jq -s -c .)
          
          echo "Final configurations JSON: $JSON_ARRAY"
          echo "Total configurations: ${#UNIQUE_CONFIGS[@]}"

          if [ "$WRAPPER_INTERNAL_CHANGED" = "true" ] || [ "$WRAPPER_CLIENT_CHANGED" = "true" ]; then
            echo "::notice::Wrapper modules changed - triggered ${#UNIQUE_CONFIGS[@]} environments"
          elif [ -n "$CLIENT_COMMON_TFVARS" ]; then
            echo "::notice::Client common.tfvars changed - triggered ${#UNIQUE_CONFIGS[@]} environments"
          else
            echo "::notice::Tfvars changes detected - triggered ${#UNIQUE_CONFIGS[@]} specific environments"
          fi

          echo "workdirs_json=$JSON_ARRAY" >> $GITHUB_OUTPUT
          echo "has_changes=true" >> $GITHUB_OUTPUT
        shell: bash
  terraform-operations:
    name: Terraform (${{ matrix.config.client && matrix.config.env && format('{0}/{1}', matrix.config.client, matrix.config.env) || '00_internal' }})
    needs: discover-changed-tf-roots
    if: needs.discover-changed-tf-roots.outputs.has_changes == 'true'
    runs-on: ubuntu-latest
    permissions:
      contents: "read"
      id-token: "write"
    strategy:
      fail-fast: false
      max-parallel: 1 
      matrix:
        config: ${{ fromJson(needs.discover-changed-tf-roots.outputs.workdirs_json) }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
  
      - name: Authenticate to Google Cloud
        id: auth
        uses: google-github-actions/auth@v2
        with:
          workload_identity_provider: ${{ secrets.GCP_WORKLOAD_IDENTITY_PROVIDER }}
          service_account: ${{ secrets.TERRAFORM_SERVICE_ACCOUNT }}

      - name: Set up Google Cloud SDK
        id: setup_gcloud
        uses: google-github-actions/setup-gcloud@v2

      - name: Setup Terraform
        id: setup_terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Generate Backend Configuration
        id: backend_config
        run: |
          WORKDIR="${{ matrix.config.workdir }}"
          BACKEND_PREFIX="${{ matrix.config.backend_prefix }}"
          
          cat > backend.hcl << EOF
          bucket = "gcs-saascada-terraform-state-01"
          prefix = "$BACKEND_PREFIX"
          EOF
          
          echo "Generated backend.hcl:"
          cat backend.hcl
        working-directory: ${{ matrix.config.workdir }}

      - name: TF Init in (${{ matrix.config.client && matrix.config.env && format('{0}/{1}', matrix.config.client, matrix.config.env) || '00_internal' }})
        id: init
        run: terraform init -backend-config=backend.hcl
        working-directory: ${{ matrix.config.workdir }}

      - name: TF Validate in (${{ matrix.config.client && matrix.config.env && format('{0}/{1}', matrix.config.client, matrix.config.env) || '00_internal' }})
        id: validate
        run: terraform validate
        working-directory: ${{ matrix.config.workdir }}

      - name: TF Plan for (${{ matrix.config.client && matrix.config.env && format('{0}/{1}', matrix.config.client, matrix.config.env) || '00_internal' }})
        id: plan
        run: |
          echo "Running Terraform Plan in directory: ${{ matrix.config.workdir }}"
          terraform plan -input=false -out=${{ env.TF_PLAN_FILE }} -detailed-exitcode  -var-file="${{ matrix.config.tfvars_file }}" -var-file="${{ matrix.config.common_tfvars_file }}"
          TF_PLAN_EXIT_CODE=$?
          echo "exitcode=${TF_PLAN_EXIT_CODE}" >> $GITHUB_OUTPUT

          if [ $TF_PLAN_EXIT_CODE -eq 1 ]; then
            echo "::error::Terraform Plan failed with an error (exit code 1)."
            exit 1
          elif [ $TF_PLAN_EXIT_CODE -eq 0 ]; then
            echo "Terraform Plan completed successfully - no changes detected (exit code 0)."
            if [ ! -f "${{ env.TF_PLAN_FILE }}" ]; then
              echo "::error::Terraform plan file was not created for no-changes plan."
              exit 1
            fi
            exit 0
          elif [ $TF_PLAN_EXIT_CODE -eq 2 ]; then
            echo "Terraform Plan completed successfully - changes detected (exit code 2)."
            if [ ! -f "${{ env.TF_PLAN_FILE }}" ]; then
              echo "::error::Terraform plan file was not created for changes plan."
              exit 1
            fi
            exit 0
          else
            echo "::error::Terraform Plan returned unexpected exit code: $TF_PLAN_EXIT_CODE"
            exit 1
          fi
        working-directory: ${{ matrix.config.workdir }}

      - name: TF Apply for (${{ matrix.config.client && matrix.config.env && format('{0}/{1}', matrix.config.client, matrix.config.env) || '00_internal' }})
        if: |
          github.event_name == 'pull_request' &&
          github.event.pull_request.merged == true &&
          github.event.pull_request.base.ref == 'main' &&
          (steps.plan.outputs.exitcode == '0' || steps.plan.outputs.exitcode == '2')
        run: |
          echo "Applying Terraform using plan file: ${{ env.TF_PLAN_FILE }}"
          terraform apply -auto-approve ${{ env.TF_PLAN_FILE }}
        working-directory: ${{ matrix.config.workdir }}
