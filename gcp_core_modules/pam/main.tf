

module "pam_access" {
  for_each = { for rule in var.pam_access : rule.entitlement_id => rule }
  source   = "GoogleCloudPlatform/pam/google"
  version  = "~> 2.1"

  entitlement_id                  = each.value.entitlement_id
  parent_id                       = each.value.parent_id
  parent_type                     = each.value.parent_type
  grant_service_agent_permissions = true
  max_request_duration_hours      = each.value.max_request_duration_hours

  organization_id = var.organization_id

  entitlement_requesters = each.value.entitlement_requesters
  entitlement_approvers  = each.value.entitlement_approvers
  role_bindings          = var.role_bindings

}