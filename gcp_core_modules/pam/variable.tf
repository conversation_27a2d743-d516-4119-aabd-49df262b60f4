variable "pam_access" {
  type = list(object({
    entitlement_id             = string
    parent_id                  = string
    parent_type                = string
    entitlement_requesters     = list(string)
    entitlement_approvers      = list(string)
    max_request_duration_hours = number
  }))
  description = "Configuration for the pam to the groups"
}


variable "organization_id" {
  type        = string
  default     = ""
  description = "organization_ID"
}

variable "role_bindings" {
  type = list(object({
    role                 = string
    condition_expression = optional(string)
  }))
  description = "The maximum amount of time for which access would be granted for a request. A requester can choose to ask for access for less than this duration but never more"
}