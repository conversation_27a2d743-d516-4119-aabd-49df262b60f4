##### Variables #####
variable "client_prefix" {
  type        = string
  description = "Client name"
}

variable "environment" {
  type        = string
  description = "environment name"
}

variable "project_id" {
  description = "Project id of the project that holds the network."
}

variable "network_name" {
  description = "Name of the network this set of firewall rules applies to."
}

variable "rules" {
  description = "List of custom rule definitions (refer to variables file for syntax)."
  default     = []
  type = list(object({
    name                    = string
    description             = optional(string)
    direction               = string
    priority                = number
    source_ranges           = list(string)
    destination_ranges      = list(string)
    source_tags             = optional(list(string), null)
    source_service_accounts = optional(list(string), null)
    target_tags             = optional(list(string), null)
    target_service_accounts = optional(list(string), null)
    allow = list(object({
      protocol = string
      ports    = list(string)
    }))
    deny = list(object({
      protocol = string
      ports    = list(string)
    }))
    log_config = optional(object({
      metadata = string
    }), null)
  }))
}

# variable "dir" {
#   type        = string
#   description = "direction of the firewall rule"
#   default     = null
# }


# # variable "blocked_ips" {
# #   description = "A list of IP addresses or CIDR ranges to block."
# #   type        = list(string)
# # }


# variable "armor_config" {
#   type = map(object({
#     enabled  = bool
#     priority = number
#   }))
#   description = "Configuration for Cloud Armor security policies."
# }