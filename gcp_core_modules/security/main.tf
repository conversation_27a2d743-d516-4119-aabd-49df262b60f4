resource "google_compute_firewall" "firewall" {
  for_each                = { for r in var.rules : "${r.name}-${r.direction}" => r }
  name                    = "fw-${each.value.direction == "INGRESS" ? "ig" : "eg"}-${var.client_prefix}-${var.environment}-${each.value.name}"
  description             = each.value.description
  direction               = each.value.direction
  network                 = var.network_name
  project                 = var.project_id
  source_ranges           = each.value.source_ranges
  destination_ranges      = each.value.destination_ranges
  source_tags             = each.value.source_tags
  source_service_accounts = each.value.source_service_accounts
  target_tags             = each.value.target_tags
  target_service_accounts = each.value.target_service_accounts
  priority                = each.value.priority

  dynamic "log_config" {
    for_each = lookup(each.value, "log_config") == null ? [] : [each.value.log_config]
    content {
      metadata = log_config.value.metadata
    }
  }

  dynamic "allow" {
    for_each = lookup(each.value, "allow", [])
    content {
      protocol = allow.value.protocol
      ports    = lookup(allow.value, "ports", null)

    }
  }

  dynamic "deny" {
    for_each = lookup(each.value, "deny", [])
    content {
      protocol = deny.value.protocol
      ports    = lookup(deny.value, "ports", null)
    }
  }

  lifecycle {
    prevent_destroy = false
  }
}



# ###################################################################
# ##################### Cloud Armor Policy ######################
# ###################################################################


# resource "google_compute_security_policy" "armor_policy" {
#   name        = "armor-${var.client_prefix}-${var.environment}"
#   description = "Armor policy with conditionally enabled rules."
#   project     = var.project_id

#   # Default rule to allow traffic if no other rule matches.
#   rule {
#     action   = "allow"
#     priority = 2147483647
#     match {
#       versioned_expr = "SRC_IPS_V1"
#       config {
#         src_ip_ranges = ["*"]
#       }
#     }
#     description = "Default allow rule"
#   }

#   # # Rule: Block specific IP addresses
#   # rule {
#   #   count    = length(var.blocked_ips) > 0 ? 1 : 0
#   #   action   = "deny(403)"
#   #   priority = 1
#   #   match {
#   #     versioned_expr = "SRC_IPS_V1"
#   #     config {
#   #       src_ip_ranges = var.blocked_ips
#   #     }
#   #   }
#   #   description = "Blocklist for specific IP addresses"
#   # }


#   # --- Pre-configured WAF Rules ---

#   rule {
#     count    = lookup(var.armor_config, "sql_injection", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "sql_injection", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('sqli-v33-stable')"
#     }
#     description = "OWASP Top 10: SQL Injection"
#   }

#   rule {
#     count    = lookup(var.armor_config, "cross_site_scripting", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "cross_site_scripting", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('xss-stable')"
#     }
#     description = "OWASP Top 10: Cross-site Scripting"
#   }

#   rule {
#     count    = lookup(var.armor_config, "local_file_inclusion", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "local_file_inclusion", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('lfi-stable')"
#     }
#     description = "OWASP Top 10: Local File Inclusion"
#   }

#   rule {
#     count    = lookup(var.armor_config, "remote_file_inclusion", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "remote_file_inclusion", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('rfi-stable')"
#     }
#     description = "OWASP Top 10: Remote File Inclusion"
#   }

#   rule {
#     count    = lookup(var.armor_config, "remote_code_execution", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "remote_code_execution", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('rce-stable')"
#     }
#     description = "OWASP Top 10: Remote Code Execution"
#   }

#   rule {
#     count    = lookup(var.armor_config, "method_enforcement", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(405)"
#     priority = lookup(var.armor_config, "method_enforcement", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('methodenforcement-stable')"
#     }
#     description = "Enforce allowed HTTP methods"
#   }

#   rule {
#     count    = lookup(var.armor_config, "scanner_detection", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "scanner_detection", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('scannerdetection-stable')"
#     }
#     description = "Detect common security scanners"
#   }

#   rule {
#     count    = lookup(var.armor_config, "protocol_attack", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "protocol_attack", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('protocolattack-stable')"
#     }
#     description = "Protect against protocol-level attacks"
#   }

#   rule {
#     count    = lookup(var.armor_config, "php_injection_attack", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "php_injection_attack", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('php-stable')"
#     }
#     description = "OWASP Top 10: PHP Injection"
#   }

#   rule {
#     count    = lookup(var.armor_config, "session_fixation_attack", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "session_fixation_attack", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('sessionfixation-stable')"
#     }
#     description = "OWASP Top 10: Session Fixation"
#   }

#   rule {
#     count    = lookup(var.armor_config, "java_attack", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "java_attack", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('java-stable')"
#     }
#     description = "Protect against common Java-based attacks"
#   }

#   rule {
#     count    = lookup(var.armor_config, "nodejs_attack", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "nodejs_attack", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('nodejs-stable')"
#     }
#     description = "Protect against common NodeJS-based attacks"
#   }

#   rule {
#     count    = lookup(var.armor_config, "new_discovered_vulnerabilities", { enabled = false }).enabled ? 1 : 0
#     action   = "deny(403)"
#     priority = lookup(var.armor_config, "new_discovered_vulnerabilities", { priority = 0 }).priority
#     match {
#       expr = "evaluatePreconfiguredExpr('cve-stable')"
#     }
#     description = "Mitigation for newly discovered vulnerabilities (CVEs)"
#   }
# }
