output "vpc_id" {
  value = google_compute_network.vpc.id
}

output "self_link" {
  value = google_compute_network.vpc.self_link
}

output "name" {
  value = google_compute_network.vpc.name
}

output "subnet_self_links" {
  description = "The self-links of the created subnets."
  value       = [for s in google_compute_subnetwork.subnet : s.self_link]
}

output "psa_name" {
  value = google_compute_global_address.private_service_access_address.name
}