
##########################################################################
#######################    VPC  custom module   ##########################
##########################################################################


resource "google_compute_network" "vpc" {
  project                         = var.project_id
  name                            = "vpc-${var.client_prefix}-${var.environment}"
  auto_create_subnetworks         = var.auto_create_subnetworks
  mtu                             = var.mtu
  routing_mode                    = var.vpc_routing_mode
  delete_default_routes_on_create = var.delete_default_routes_on_create
}





##########################################################################
#######################   subnet custom module  ##########################
##########################################################################


resource "google_compute_subnetwork" "subnet" {
  for_each                 = var.subnet_config
  name                     = "sb-${var.client_prefix}-${var.environment}-${each.value.subnet_purpose}-${var.region_prefix}"
  project                  = var.project_id
  ip_cidr_range            = each.value.primary_cidr_range
  region                   = var.region
  network                  = google_compute_network.vpc.id
  private_ip_google_access = lookup(each.value, "private_ip_google_access")


  # dynamic "secondary_ip_range" {
  #   for_each = each.value.secondary_cidr_ranges
  #   content {
  #     range_name    = secondary_ip_range.value.range_name
  #     ip_cidr_range = secondary_ip_range.value.ip_cidr_range
  #   }
  # }
}


##########################################################################
####################   cloud Router custom module  #######################
##########################################################################

resource "google_compute_router" "cloud_router" {
  project = var.project_id
  name    = "cr-${var.client_prefix}-${var.environment}-${var.region_prefix}-nat" ###
  region  = var.region
  network = google_compute_network.vpc.id

  bgp {
    asn = var.cloud_router_bgp_asn
  }
  depends_on = [google_compute_network.vpc]
}




##########################################################################
####################   NAT gateway  custom module  #######################
##########################################################################



##################### Reserving External IP  #############################


resource "google_compute_address" "ext_ip_address" {
  count        = var.nat_ip_count
  project      = var.project_id
  name         = "ext-ip-nat-${var.client_prefix}-${var.environment}-${var.region_prefix}-0${count.index + 1}"
  address_type = "EXTERNAL"
  region       = var.region
}


######################## Cloud NAT Gateway  ###############################


resource "google_compute_router_nat" "nat_manual" {
  project                = var.project_id
  name                   = "nat-${var.client_prefix}-${var.environment}-${var.region_prefix}"
  router                 = google_compute_router.cloud_router.name
  region                 = var.region
  min_ports_per_vm       = 64
  nat_ip_allocate_option = "MANUAL_ONLY"
  nat_ips                = google_compute_address.ext_ip_address.*.self_link

  source_subnetwork_ip_ranges_to_nat = "ALL_SUBNETWORKS_ALL_IP_RANGES"

  depends_on = [google_compute_router.cloud_router]
}

###############################################################
##### Google Private service access/connection IP reserve #####
###############################################################

resource "google_compute_global_address" "private_service_access_address" {
  name          = "psa-${var.client_prefix}-${var.environment}"
  project       = var.project_id
  purpose       = "VPC_PEERING"
  address_type  = "INTERNAL"
  address       = split("/", var.psa_cidr)[0]
  network       = google_compute_network.vpc.id
  prefix_length = split("/", var.psa_cidr)[1]
}


############################################
##### Private Service Access/Connection ####
############################################

resource "google_service_networking_connection" "psa_connection" {
  network                 = google_compute_network.vpc.id
  service                 = "servicenetworking.googleapis.com"
  reserved_peering_ranges = [google_compute_global_address.private_service_access_address.name]
  depends_on = [google_compute_global_address.private_service_access_address, google_compute_network.vpc]
}


resource "google_compute_network_peering_routes_config" "peering_routes" {
  project = var.project_id
  peering = google_service_networking_connection.psa_connection.peering
  network = google_compute_network.vpc.name

  import_custom_routes = true
  export_custom_routes = true

  depends_on = [google_service_networking_connection.psa_connection]
}