variable "project_id" {
  type        = string
  description = "The project ID to deploy to"
  default     = ""
}

variable "client_prefix" {
  type        = string
  description = "prefix for client"
}

variable "environment" {
  type        = string
  description = "environment to be deployed"
}

variable "auto_create_subnetworks" {
  type        = bool
  description = "If you need automatic subnet creation"
  default     = false
}

variable "routing_mode" {
  type        = string
  description = "REGIONAL or GLOBAL"
  default     = "GLOBAL"
}

variable "mtu" {
  type        = number
  description = "Maximum Transmission Unit in bytes. Minimum value is 1300"
  default     = 1460
}

variable "delete_default_routes_on_create" {
  type        = bool
  description = "If you want default routs to be created."
  default     = false
}

variable "region" {
  type        = string
  description = "region for the deployment of resources"
  default     = "europe-west2"
}


variable "subnet_config" {
  type = map(object({
    subnet_purpose           = string
    primary_cidr_range       = string
    private_ip_google_access = optional(bool, true)

    # secondary_cidr_ranges = optional(list(object({
    #   range_name    = string
    #   ip_cidr_range = string
    # })))

  }))
  description = "Configuration for the subnets."
}

variable "region_prefix" {
  type        = string
  description = "Prefix for region"
  default     = "ew2"
}

variable "cloud_router_bgp_asn" {
  type        = string
  default     = "64512"
  description = "ASN Number for the NAT "
}


variable "vpc_routing_mode" {
  type        = string
  default     = "GLOBAL"
  description = "Dynamic routing mode GLOBAL/REGIONAL"
}

variable "nat_ip_count" {
  type        = number
  default     = 2
  description = "Number of External Ips to be reserved"
}

variable "psa_cidr" {
  type        = string
  description = "CIDR range for PSA"
  default     = ""
}

variable "psa_name" {
  type        = string
  description = "Name for the PSA"
  default     = ""
}

variable "prefix_length" {
  type        = number
  description = "The prefix length of the IP range"
  default     = 21
}

variable "peering_network" {
  type        = string
  description = "The name of the primary network for the peering."
  default     = ""
}

variable "reserved_peering_ranges" {
  type        = list(string)
  description = "The list of reserved peering ranges"
  default     = []
}