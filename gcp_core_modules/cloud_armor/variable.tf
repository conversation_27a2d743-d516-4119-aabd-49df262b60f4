##### Variables #####
variable "client_prefix" {
  type        = string
  description = "Client name"
}

variable "environment" {
  type        = string
  description = "environment name"
}

variable "project_id" {
  description = "Project id of the project that holds the network."
}


# variable "blocked_ips" {
#   description = "A list of IP addresses or CIDR ranges to block."
#   type        = list(string)
# }


variable "armor_config" {
  type = map(object({
    enabled  = bool
    priority = number
  }))
  description = "Configuration for Cloud Armor security policies."
}