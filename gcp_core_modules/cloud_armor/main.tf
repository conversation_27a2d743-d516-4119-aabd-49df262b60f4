###################################################################
##################### Cloud Armor Policy ######################
###################################################################


resource "google_compute_security_policy" "armor_policy" {
  name        = "armor-${var.client_prefix}-${var.environment}"
  description = "Armor policy with conditionally enabled rules."
  project     = var.project_id

  # Default rule to allow traffic if no other rule matches.
  dynamic "rule" {
    for_each = [1]
    content {
      action   = "allow"
      priority = 2147483647
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Default allow rule"
    }
  }

  # # Rule: Block specific IP addresses
  # rule {
  #   count    = length(var.blocked_ips) > 0 ? 1 : 0
  #   action   = "deny(403)"
  #   priority = 1
  #     match {
  #     versioned_expr = "SRC_IPS_V1"
  #     config {
  #       src_ip_ranges = var.blocked_ips
  #     }
  #   }
  #   description = "Blocklist for specific IP addresses"
  # } 


  # --- Pre-configured WAF Rules ---

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "sql_injection" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["sql_injection"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }

      }
      description = "OWASP Top 10: SQL Injection"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "cross_site_scripting" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["cross_site_scripting"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "OWASP Top 10: Cross-site Scripting"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "local_file_inclusion" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["local_file_inclusion"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "OWASP Top 10: Local File Inclusion"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "remote_file_inclusion" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["remote_file_inclusion"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "OWASP Top 10: Remote File Inclusion"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "remote_code_execution" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["remote_code_execution"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "OWASP Top 10: Remote Code Execution"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "method_enforcement" && v.enabled]
    content {
      action   = "deny(405)"
      priority = var.armor_config["method_enforcement"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Enforce allowed HTTP methods"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "scanner_detection" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["scanner_detection"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Detect common security scanners"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "protocol_attack" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["protocol_attack"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Protect against protocol-level attacks"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "php_injection_attack" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["php_injection_attack"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "OWASP Top 10: PHP Injection"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "session_fixation_attack" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["session_fixation_attack"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "OWASP Top 10: Session Fixation"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "java_attack" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["java_attack"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Protect against common Java-based attacks"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "nodejs_attack" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["nodejs_attack"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Protect against common NodeJS-based attacks"
    }
  }

  dynamic "rule" {
    for_each = [for k, v in var.armor_config : k if k == "new_discovered_vulnerabilities" && v.enabled]
    content {
      action   = "deny(403)"
      priority = var.armor_config["new_discovered_vulnerabilities"].priority
      match {
        versioned_expr = "SRC_IPS_V1"
        config {

          src_ip_ranges = ["*"]
        }
      }
      description = "Mitigation for newly discovered vulnerabilities (CVEs)"
    }
  }
}
