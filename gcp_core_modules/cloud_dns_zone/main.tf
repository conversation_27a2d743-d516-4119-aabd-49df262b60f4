##################################
########## Private Zone ##########
##################################

resource "google_dns_managed_zone" "private_zone" {
  count         = var.type == "private" ? 1 : 0
  project       = var.project_id
  name          = var.environment == "prod" ? "private-${var.client_prefix}-saascada-io" : "private-${var.client_prefix}-${var.environment}-saascada-io"
  dns_name      = var.environment == "prod" ? "${var.client_prefix}.saascada.io." : "${var.client_prefix}.${var.environment}.saascada.io."
  description   = var.description
  labels        = var.labels
  visibility    = "private"
  force_destroy = var.force_destroy

  dynamic "private_visibility_config" {
    for_each = length(var.network_names) > 0 ? [1] : []
    content {
      dynamic "networks" {
        for_each = toset(var.network_names)
        content {
          network_url = data.google_compute_network.network[networks.value].id
        }
      }
    }
  }
}

##################################
########## Public Zone ###########
##################################

resource "google_dns_managed_zone" "public" {
  count         = var.type == "public" ? 1 : 0
  project       = var.project_id
  name          = var.environment == "prod" ? "public-${var.client_prefix}-saascada-io" : "public-${var.client_prefix}-${var.environment}-saascada-io"
  dns_name      = var.environment == "prod" ? "${var.client_prefix}.saascada.io." : "${var.client_prefix}.${var.environment}.saascada.io."
  description   = var.description
  labels        = var.labels
  visibility    = "public"
  force_destroy = var.force_destroy

  dynamic "dnssec_config" {
    for_each = length(var.dnssec_config) == 0 ? [] : [var.dnssec_config]
    iterator = config
    content {
      kind          = lookup(config.value, "kind", "dns#managedZoneDnsSecConfig")
      non_existence = lookup(config.value, "non_existence", "nsec3")
      state         = lookup(config.value, "state", "off")

      default_key_specs {
        algorithm  = lookup(var.default_key_specs_key, "algorithm", "rsasha256")
        key_length = lookup(var.default_key_specs_key, "key_length", 2048)
        key_type   = lookup(var.default_key_specs_key, "key_type", "keySigning")
        kind       = lookup(var.default_key_specs_key, "kind", "dns#dnsKeySpec")
      }
      default_key_specs {
        algorithm  = lookup(var.default_key_specs_zone, "algorithm", "rsasha256")
        key_length = lookup(var.default_key_specs_zone, "key_length", 1024)
        key_type   = lookup(var.default_key_specs_zone, "key_type", "zoneSigning")
        kind       = lookup(var.default_key_specs_zone, "kind", "dns#dnsKeySpec")
      }
    }
  }

  cloud_logging_config {
    enable_logging = var.enable_logging
  }
}

