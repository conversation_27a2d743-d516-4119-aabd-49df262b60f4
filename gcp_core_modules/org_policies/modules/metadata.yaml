# Copyright 2022 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

apiVersion: blueprints.cloud.google.com/v1alpha1
kind: BlueprintMetadata
metadata:
  name: terraform-google-org-policy
  annotations:
    config.kubernetes.io/local-config: "true"
spec:
  title: Google Cloud Organization Policy Terraform Module
  source:
    repo: https://github.com/terraform-google-modules/terraform-google-org-policy.git
    sourceType: git
  version: 5.3.0
  actuationTool:
    type: Terraform
    version: '>= 0.13'
  subBlueprints:
  - name: bucket_policy_only
    location: modules/bucket_policy_only
  - name: domain_restricted_sharing
    location: modules/domain_restricted_sharing
  - name: org_policy_v2
    location: modules/org_policy_v2
  - name: restrict_vm_external_ips
    location: modules/restrict_vm_external_ips
  - name: skip_default_network
    location: modules/skip_default_network
  examples:
  - name: basic_org_policies
    location: examples/basic_org_policies
  - name: boolean_org_exclude
    location: examples/boolean_org_exclude
  - name: boolean_project_allow
    location: examples/boolean_project_allow
  - name: list_folder_deny
    location: examples/list_folder_deny
  - name: list_org_exclude
    location: examples/list_org_exclude
  - name: v2_boolean_org_enforce
    location: examples/v2_boolean_org_enforce
  variables:
  - name: allow
    description: (Only for list constraints) List of values which should be allowed
    type: list(string)
    default:
    - ""
    required: false
  - name: allow_list_length
    description: The number of elements in the allow list
    type: number
    default: 0
    required: false
  - name: constraint
    description: The constraint to be applied
    type: string
    required: true
  - name: deny
    description: (Only for list constraints) List of values which should be denied
    type: list(string)
    default:
    - ""
    required: false
  - name: deny_list_length
    description: The number of elements in the deny list
    type: number
    default: 0
    required: false
  - name: enforce
    description: If boolean constraint, whether the policy is enforced at the root; if list constraint, whether to deny all (true) or allow all
    type: bool
    required: false
  - name: exclude_folders
    description: Set of folders to exclude from the policy
    type: set(string)
    default: []
    required: false
  - name: exclude_projects
    description: Set of projects to exclude from the policy
    type: set(string)
    default: []
    required: false
  - name: folder_id
    description: The folder id for putting the policy
    type: string
    required: false
  - name: organization_id
    description: The organization id for putting the policy
    type: string
    required: false
  - name: policy_for
    description: 'Resource hierarchy node to apply the policy to: can be one of `organization`, `folder`, or `project`.'
    type: string
    required: true
  - name: policy_type
    description: The constraint type to work with (either 'boolean' or 'list')
    type: string
    default: list
    required: false
  - name: project_id
    description: The project id for putting the policy
    type: string
    required: false
  roles:
  - level: Project
    roles:
    - roles/orgpolicy.policyAdmin
  - level: Project
    roles:
    - roles/owner
  services:
  - cloudresourcemanager.googleapis.com
  - storage-api.googleapis.com
  - serviceusage.googleapis.com
  - orgpolicy.googleapis.com
