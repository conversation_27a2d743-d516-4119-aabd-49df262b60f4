locals {
  organization_id = split("/", var.parent)[0] == "organizations" ? split("/", var.parent)[1] : null
  folder_id       = split("/", var.parent)[0] == "folders" ? split("/", var.parent)[1] : null
  policy_for      = split("/", var.parent)[0] == "organizations" ? "organization" : "folder"
}

# #**************************************************************/
# #IAM
#Defines the set of allowed domain members that can be added to Cloud IAM policies.
module "iam_domain_restricted_sharing" {
  source           = "./modules/domain_restricted_sharing/"
  organization_id  = local.organization_id
  folder_id        = local.folder_id
  policy_for       = local.policy_for
  domains_to_allow = var.iam_domain_restricted_sharing
}

# #Defines the set of domains that email addresses added to Essential Contacts can have.
module "iam_allowed_contact_domains" {
  source                  = "./modules/allowed_contact_domain"
  organization_id         = local.organization_id
  folder_id               = local.folder_id
  policy_for              = local.policy_for
  allowed_contact_domains = var.iam_allowed_contact_domains
}

#####
#When ture, disables the creation of service account keys for all service accounts in the organization.
module "iam_disableServiceAccountKeyCreation" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "iam.disableServiceAccountKeyCreation"
  policy_type     = "boolean"
  enforce         = var.iam_disableServiceAccountKeyCreation
}

#When true, disables the upload of service account keys for all service accounts in the organization.
module "iam_disableServiceAccountKeyUpload" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "iam.disableServiceAccountKeyUpload"
  policy_type     = "boolean"
  enforce         = var.iam_disableServiceAccountKeyUpload
}

#when true, restricts automatic IAM grants for default service accounts.
module "iam_automaticIamGrantsForDefaultServiceAccounts" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "iam.automaticIamGrantsForDefaultServiceAccounts"
  policy_type     = "boolean"
  enforce         = var.iam_automaticIamGrantsForDefaultServiceAccounts
}

#Compute

#When true, restricts the set of external IP addresses that can be used by VM instances.
module "org_vm_external_ip_access" {
  source          = "./modules/restrict_vm_external_ips"
  organization_id = local.organization_id
  folder_id       = local.folder_id
  policy_for      = local.policy_for
  vms_to_allow    = var.org_vm_external_ip_access
  # enforce      = var.org_vm_external_ip_access
}

#when set to True, disables the creation of or update to subnetworks with a stack_type of IPV4_IPV6 and ipv6_access_type of EXTERNAL.
module "compute_disableVpcExternalIpv6" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "compute.disableVpcExternalIpv6"
  policy_type     = "boolean"
  enforce         = var.compute_disableVpcExternalIpv6
}
#####
#when set to True, disables nested Virtualization for all compute engine VMs.
module "compute_disableNestedVirtualization" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "compute.disableNestedVirtualization"
  policy_type     = "boolean"
  enforce         = var.compute_disableNestedVirtualization
}
#Restricts creation of protocol forwarding rules to specific types
module "org_restrict_protocol_forwarding_creation_for_types" { #####################################
  source               = "./modules/restrict_protocol_forwarding_creation_for_types"
  organization_id      = local.organization_id
  folder_id            = local.folder_id
  policy_for           = local.policy_for
  forwarding_rule_type = var.org_restrict_protocol_forwarding_creation_for_types
}
####
#Database
#When true, restricts public IP addresses on Cloud SQL instances.
module "sql_restrictPublicIp" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "sql.restrictPublicIp"
  policy_type     = "boolean"
  enforce         = var.sql_restrictPublicIp
}

module "sql_restrictAuthorizedNetworks" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "sql.restrictAuthorizedNetworks"
  policy_type     = "boolean"
  enforce         = var.sql_restrictAuthorizedNetworks
}

# #Cloud Storage
# #When true, public access is restricted for all buckets and objects, both new and existing, under that resource.
module "storage_bucket_publicAccessPrevention" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "storage.publicAccessPrevention"
  policy_type     = "boolean"
  enforce         = var.storage_bucket_publicAccessPrevention
}

# #When true, requires buckets to use uniform IAM-based bucket-level access.
module "storage_uniformBucketLevelAccess" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "storage.uniformBucketLevelAccess"
  policy_type     = "boolean"
  enforce         = var.storage_uniformBucketLevelAccess
}

# #Networking
# #When true, restricts the  users from deleting the shared vpc.
module "compute_network_restrictXpnProjectLienRemoval" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "compute.restrictXpnProjectLienRemoval"
  policy_type     = "boolean"
  enforce         = var.compute_network_restrictXpnProjectLienRemoval
}

# #When True it skips the creation of the default network and related resources during Google Cloud Platform Project resource creation.
module "compute_network_skipDefaultNetworkCreation" {
  source          = "./modules"
  policy_for      = local.policy_for
  folder_id       = local.folder_id
  organization_id = local.organization_id
  constraint      = "compute.skipDefaultNetworkCreation"
  policy_type     = "boolean"
  enforce         = var.compute_network_skipDefaultNetworkCreation
}

# GEO Location Restriction
# Defines the set of allowed locations for resource creation.
module "gcp_restrictResourceLocations" {
  source            = "./modules"
  policy_for        = local.policy_for
  folder_id         = local.folder_id
  organization_id   = local.organization_id
  constraint        = "gcp.resourceLocations"
  policy_type       = "list"
  allow_list_length = length([var.gcp_restrictResourceLocations])
  allow             = var.gcp_restrictResourceLocations
}
