/**
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

variable "resources" {
  description = "Export logs from the specified resources."
  type        = map(string)

  validation {
    condition     = length(var.resources) > 0
    error_message = "The resources map should have at least 1 item."
  }
}

variable "resource_type" {
  description = "Resource type of the resource that will export logs to destination. Must be: project, organization, or folder."
  type        = string

  validation {
    condition     = contains(["project", "folder", "organization"], var.resource_type)
    error_message = "The resource_type value must be: project, organization, or folder."
  }
}

variable "logging_project_key" {
  description = "(Optional) The key of logging destination project if it is inside resources map. It is mandatory when resource_type = project and logging_target_type = logbucket."
  type        = string
  default     = ""
}

variable "logging_destination_project_id" {
  description = "The ID of the project that will have the resources where the logs will be created."
  type        = string
}


#----------------------------- #
# Logbucket specific variables #
#----------------------------- #
variable "logbucket_options" {
  description = <<EOT
Destination LogBucket options:
- logging_sink_name: The name of the log sink to be created.
- logging_sink_filter: The filter to apply when exporting logs. Only log entries that match the filter are exported. Default is '' which exports all logs.
- name: The name of the log bucket to be created and used for log entries matching the filter.
- location: The location of the log bucket. Default: global.
- retention_days: (Optional) The number of days data should be retained for the log bucket. Default 30.

Destination LogBucket options example:
```
logbucket_options = {
  logging_sink_name   = "sk-c-logging-logbkt"
  logging_sink_filter = ""
  name                = "logbkt-org-logs"
  retention_days      = "30"
  location            = "global"
}
```
EOT
  type        = map(any)
  default     = null

  validation {
    condition     = var.logbucket_options == null ? true : !contains(keys(var.logbucket_options), "retention_days") ? true : can(tonumber(var.logbucket_options["retention_days"]))
    error_message = "Retention days must be a number. Default 30 days."
  }
}

#--------------------------- #
# Storage specific variables #
#--------------------------- #
variable "storage_options" {
  description = <<EOT
Destination Storage options:
- logging_sink_name: The name of the log sink to be created.
- logging_sink_filter: The filter to apply when exporting logs. Only log entries that match the filter are exported. Default is '' which exports all logs.
- storage_bucket_name: The name of the storage bucket to be created and used for log entries matching the filter.
- location: (Optional) The location of the logging destination. Default: US.
- Retention Policy variables: (Optional) Configuration of the bucket's data retention policy for how long objects in the bucket should be retained.
  - retention_policy_is_locked: Set if policy is locked.
  - retention_policy_period_days: Set the period of days for log retention. Default: 30.
- versioning: (Optional) Toggles bucket versioning, ability to retain a non-current object version when the live object version gets replaced or deleted.
- force_destroy: When deleting a bucket, this boolean option will delete all contained objects.

Destination Storage options example:
```
storage_options = {
  logging_sink_name   = "sk-c-logging-bkt"
  logging_sink_filter = ""
  storage_bucket_name = "bkt-org-logs"
  location            = "US"
  force_destroy       = false
  versioning          = false
}
```
EOT
  type        = map(any)
  default     = null


}


variable "lifecycle_rules" {
  type = list(object({
    action = object({
      type          = optional(string)
      storage_class = optional(string)
    })
    condition = object({
      age = optional(number)
    })
  }))
  description = "value"
  default = [
    {
      action = {
        type          = "SetStorageClass"
        storage_class = "NEARLINE" # Set your desired storage class for the rule
      }
      condition = {
        age = 30 # Set your desired age in days
      }
    },
    {
      action = {
        type          = "SetStorageClass"
        storage_class = "COLDLINE"
      }
      condition = {
        age = 90
      }
    },
    {
      action = {
        type = "Delete"
      }
      condition = {
        age = 400
      }
    }
  ]
}