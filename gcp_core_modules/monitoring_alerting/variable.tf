
#####################
##### Variables #####
#####################

variable "alerting_project_id" {
  description = "Project ID of the Operations project"
  type        = string
  default     = ""
}

variable "monitored_project_ids" {
  type        = list(string)
  description = "List of projects which need to be added in the monitored project list"
}

variable "alert_config" {
  type = list(object({
    create_firewall_metric      = optional(bool, false)
    create_route_metric         = optional(bool, false)
    create_network_metric       = optional(bool, false)
    create_iam_metric           = optional(bool, false)
    create_custom_role_metric   = optional(bool, false)
    create_owner_role_metric    = optional(bool, false)
    create_bucket_iam_metric    = optional(bool, false)
    create_gce_alert            = optional(bool, false)
    create_sql_alert            = optional(bool, false)
    create_pubsub_alert         = optional(bool, false)
    create_gke_alert            = optional(bool, false)
    create_redis_alert          = optional(bool, false)
    create_cloud_run_alert      = optional(bool, false)
    create_cloud_function_alert = optional(bool, false)
  }))
}





variable "project_ids" {
  type = list(string)
  description = "Project IDs"
  default     = []
}

variable "notification_channels" {
  type = list(string)
  description = "List of notification channels"
  default     = []
}

