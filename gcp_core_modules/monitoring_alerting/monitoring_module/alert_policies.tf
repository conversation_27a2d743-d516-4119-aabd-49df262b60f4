###########################
###### Alert Policies #####
###########################

###############################################
################ Firewall Alert ###############
###############################################

#Firewall Alert from Log based metric
resource "google_monitoring_alert_policy" "firewall_alert_policy" {
  for_each     = var.create_firewall_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Firewall_Rule_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "Firewall Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_firewall_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Firewall Rule change in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.firewall_metric]
}



###############################################
################ Network Alert ################
###############################################

#Network Alert from Log based metric
resource "google_monitoring_alert_policy" "network_alert_policy" {
  for_each     = var.create_network_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Networking_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "Network Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_network_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Updated networking in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.network_metric]
}


###############################################
############### Route Alert ###################
###############################################

#Route Alert from Log based metric
resource "google_monitoring_alert_policy" "route_alert_policy" {
  for_each     = var.create_route_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Routing_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "Route Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_route_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Updated Routing in  project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.network_metric]
}



################################################
###################### IAM #####################
################################################

# IAM Alert from Log based metric

resource "google_monitoring_alert_policy" "iam_alert_policy" {
  for_each     = var.create_iam_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_IAM_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "IAM Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_iam_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "IAM Change in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.iam_metric]
}



################################################
################## Custom Role #################
################################################

# Custom Role Alert from Log based metric

resource "google_monitoring_alert_policy" "custom_role_alert_policy" {
  for_each     = var.create_custom_role_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Custom_Role_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "Custom Role Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_custom_role_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Custom Role change in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.custom_role_metric]
}



################################################
################## Owner Role ##################
################################################

# Owner Role Alert from Log based metric

resource "google_monitoring_alert_policy" "owner_role_alert_policy" {
  for_each     = var.create_custom_role_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Owner_Role_Alert"
  enabled      = true
  combiner     = "OR"

  conditions {
    display_name = "Owner Role Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_owner_role_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Owner Role change in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.owner_role_metric]
}



################################################
################## Bucket IAM ##################
################################################

# Bucket Level IAM Permissions Alert from Log based metric

resource "google_monitoring_alert_policy" "bucket_iam_alert_policy" {
  for_each     = var.create_bucket_iam_metric ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Bucket_IAM_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "Bucket IAM Permissions Alert"
    condition_threshold {
      filter = "resource.type = \"logging_bucket\" AND metric.type = \"logging.googleapis.com/user/${each.value}_bucket_iam_logging_metrics\""
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_COUNT"
      }

      comparison = "COMPARISON_GT"
      duration   = "0s"
      trigger {
        count = 1
      }
      threshold_value = 1
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Bucket level IAM Permission change in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels

  depends_on = [google_logging_metric.bucket_iam_metric]
}



###############################################
################### GCE #######################
###############################################

resource "google_monitoring_alert_policy" "gce_alert_policy" {
  for_each     = var.create_gce_alert ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_GCE_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "CPU Utilization"
    condition_threshold {
      filter     = "metric.type=\"compute.googleapis.com/instance/cpu/utilization\" AND resource.type=\"gce_instance\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 0.8
      trigger {
        count = 2
      }
    }
  }
  conditions {
    display_name = "Memory Utilization"
    condition_threshold {
      filter     = "metric.type=\"agent.googleapis.com/memory/percent_used\" resource.type=\"gce_instance\" metric.label.\"state\"=\"used\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 80
      trigger {
        count = 2
      }
    }
  }
  conditions {
    display_name = "Disk Utilization"
    condition_threshold {
      filter     = "metric.type=\"agent.googleapis.com/disk/percent_used\" resource.type=\"gce_instance\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 90
      trigger {
        count = 1
      }
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Compute Engine in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels
}



###############################################
################# Cloud SQL ###################
###############################################

resource "google_monitoring_alert_policy" "cloud_sql_alert_policy" {
  for_each     = var.create_sql_alert ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Cloud_SQL_Alert"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "Cloud SQL Database - CPU utilization"
    condition_threshold {
      filter          = "resource.type = \"cloudsql_database\" AND metric.type = \"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration        = "0s"
      threshold_value = 0.8
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Cloud SQL Database - Disk utilization"
    condition_threshold {
      filter          = "resource.type = \"cloudsql_database\" AND metric.type = \"cloudsql.googleapis.com/database/disk/utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration        = "0s"
      threshold_value = 80
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Cloud SQL Database - Memory utilization"
    condition_threshold {
      filter          = "resource.type = \"cloudsql_database\" AND metric.type = \"cloudsql.googleapis.com/database/memory/utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration        = "0s"
      threshold_value = 90
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_MEAN"
      }
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Cloud SQL Instance Database - Latency per query"
    condition_threshold {
      filter          = "resource.type = \"cloudsql_instance_database\" AND metric.type = \"dbinsights.googleapis.com/perquery/latencies\" AND resource.labels.project_id=\"${each.value}\""
      duration        = "0s"
      threshold_value = 30000
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_MEAN"
        per_series_aligner   = "ALIGN_DELTA"
      }
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Cloud SQL Database - MySQL Connections Count"
    condition_threshold {
      filter          = "resource.type = \"cloudsql_database\" AND metric.type = \"cloudsql.googleapis.com/database/mysql/connections_count\" AND resource.labels.project_id=\"${each.value}\""
      duration        = "0s"
      threshold_value = 80
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_DELTA"
      }
      trigger {
        count = 1
      }
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Cloud SQL in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels
}



################################################
#################### Pub/Sub ###################
################################################

resource "google_monitoring_alert_policy" "pub_sub_alert_policy" {
  for_each     = var.create_pubsub_alert ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_Pub/Sub_Alert"
  combiner     = "OR"
  enabled      = true

  conditions {
    display_name = "Cloud Pub/Sub Subscription - Delivery latency health score"
    condition_threshold {
      filter          = "resource.type = \"pubsub_subscription\" AND metric.type = \"pubsub.googleapis.com/subscription/delivery_latency_health_score\" AND resource.labels.project_id=\"${each.value}\""
      duration        = "0s"
      threshold_value = 4
      comparison      = "COMPARISON_GT"
      aggregations {
        alignment_period     = "300s"
        cross_series_reducer = "REDUCE_NONE"
        per_series_aligner   = "ALIGN_FRACTION_TRUE"
      }
      trigger {
        count = 1
      }
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Pub/Sub in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels
}



################################################
###################### GKE #####################
################################################

#Container
resource "google_monitoring_alert_policy" "gke_alert_policy" {
  for_each     = var.create_gke_alert ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_GKE_Alert"
  enabled      = true
  combiner     = "OR"

  conditions {
    display_name = "Kubernetes Container - CPU limit utilization"
    condition_threshold {
      filter     = "resource.type = \"k8s_container\" AND metric.type = \"kubernetes.io/container/cpu/limit_utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 80
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Kubernetes Container - CPU request utilization"
    condition_threshold {
      filter     = "resource.type = \"k8s_container\" AND metric.type = \"kubernetes.io/container/cpu/request_utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 80
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Kubernetes Container - Memory limit utilization"
    condition_threshold {
      filter     = "resource.type = \"k8s_container\" AND metric.type = \"kubernetes.io/container/memory/limit_utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 80
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Kubernetes Container - Memory request utilization"
    condition_threshold {
      filter     = "resource.type = \"k8s_container\" AND metric.type = \"kubernetes.io/container/memory/request_utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 80
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Kubernetes Container - Restart count"
    condition_threshold {
      filter     = "resource.type = \"k8s_container\" AND metric.type = \"kubernetes.io/container/restart_count\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_RATE"
      }
      threshold_value = 5 //5 seconds
      trigger {
        count = 1
      }
    }
  }

  conditions {
    display_name = "Kubernetes Container - Uptime"
    condition_threshold {
      filter     = "resource.type = \"k8s_container\" AND metric.type = \"kubernetes.io/container/uptime\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_LT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 0.01 //10ms
      trigger {
        count = 1
      }
    }
  }

  documentation {
    mime_type = "text/markdown"
    content   = "GKE in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels
}



#######################################################
#################### Customer Quota ###################
#######################################################

resource "google_monitoring_alert_policy" "quota_alert_policy" {
  for_each     = toset(var.project_ids)
  project      = var.alerting_project_id
  display_name = "${each.value}_Customer_Quota_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "Quota Monitoring Condition"
    condition_monitoring_query_language {
      query    = "fetch consumer_quota| filter resource.service =~ '.*'| { t_0: metric 'serviceruntime.googleapis.com/quota/allocation/usage' | align next_older(1d) | group_by [resource.project_id, metric.quota_metric, resource.location], [value_usage_max: max(value.usage)] ; t_1: metric 'serviceruntime.googleapis.com/quota/limit' | align next_older(1d) | group_by [resource.project_id, metric.quota_metric, resource.location], [value_limit_min: min(value.limit)] }| ratio| every 1m| condition gt(ratio, 0.7 '1')"
      duration = "60s"
      trigger {
        count = "1"
      }
    }
  }
  alert_strategy {
    auto_close = "86400s"
  }

  documentation {
    mime_type = "text/markdown"
    content   = "Customer Quota Limit Utilization has generated this alert."
  }

  notification_channels = var.notification_channels
}


###############################################
################## REDIS ######################
###############################################

resource "google_monitoring_alert_policy" "redis_alert_policy" {
  for_each     = var.create_redis_alert ? toset(var.project_ids) : []
  project      = var.alerting_project_id
  display_name = "${each.value}_REDIS_Alert"
  enabled      = true
  combiner     = "OR"
  conditions {
    display_name = "CPU Seconds"
    condition_threshold {
      filter     = "resource.type = \"redis_instance\" AND metric.type = \"redis.googleapis.com/stats/cpu_utilization\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 2
      trigger {
        count = 2
      }
    }
  }
  conditions {
    display_name = "Memory Usage"
    condition_threshold {
      filter     = "resource.type = \"redis_instance\" AND metric.type = \"redis.googleapis.com/stats/memory/usage\" AND resource.labels.project_id=\"${each.value}\""
      duration   = "0s"
      comparison = "COMPARISON_GT"
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
      threshold_value = 80
      trigger {
        count = 2
      }
    }
  }
  documentation {
    mime_type = "text/markdown"
    content   = "Redis in project - ${each.value} has generated this alert."
  }

  notification_channels = var.notification_channels
} #