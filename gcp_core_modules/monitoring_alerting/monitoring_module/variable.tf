#######################################
##### Log based Metrics Variables #####
#######################################
variable "project_ids" {
  description = "List of project IDs"
  type        = list(string)
}

variable "create_firewall_metric" {
  description = "Whether to create firewall logging metric"
  type        = bool
  default     = false
}

variable "create_network_metric" {
  description = "Whether to create network logging metric"
  type        = bool
  default     = false
}

variable "create_route_metric" {
  description = "Whether to create route logging metric"
  type        = bool
  default     = false
}

variable "create_iam_metric" {
  description = "Whether to create IAM logging metric"
  type        = bool
  default     = false
}

variable "create_custom_role_metric" {
  description = "Whether to create Custom Role logging metric"
  type        = bool
  default     = false
}

variable "create_owner_role_metric" {
  description = "Whether to create Owner IAM role logging metric"
  type        = bool
  default     = false
}

variable "create_bucket_iam_metric" {
  description = "Whether to create Bucket level IAM logging metric"
  type        = bool
  default     = false
}

##############################
##### Alerting Variables #####
##############################

variable "notification_channels" {
  description = "List of notification channels"
  type        = list(string)
  default     = []
}

variable "product" {
  description = "Product name to be used in the alert policy display name"
  type        = string
  default     = ""
}

variable "alerting_project_id" {
  description = "Project ID of the Operations project"
  type        = string
  default     = ""
}

variable "create_gce_alert" {
  description = "Whether to create GCE alert policy"
  type        = bool
  default     = false
}

variable "create_sql_alert" {
  description = "Whether to create Cloud SQL alert policy"
  type        = bool
  default     = false
}

variable "create_pubsub_alert" {
  description = "Whether to create Pub/Sub alert policy"
  type        = bool
  default     = false
}

variable "create_gke_alert" {
  description = "Whether to create GKE alert policy"
  type        = bool
  default     = false
}

variable "create_redis_alert" {
  description = "Whether to create REDIS alert policy"
  type        = bool
  default     = false
}

variable "create_cloud_run_alert" {
  description = "Whether to create Cloud Run alert policy"
  type        = bool
  default     = false
}

variable "create_cloud_function_alert" {
  description = "Whether to create Cloud Functions alert policy"
  type        = bool
  default     = false
}
