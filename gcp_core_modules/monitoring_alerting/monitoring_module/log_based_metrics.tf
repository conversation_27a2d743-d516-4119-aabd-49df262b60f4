#############################
##### Log based Metrics #####
#############################

##### Firewall Change

resource "google_logging_metric" "firewall_metric" {
  for_each = var.create_firewall_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_firewall_logging_metrics"
  filter   = "resource.type=gce_firewall_rule AND (protoPayload.methodName:compute.firewalls.insert OR protoPayload.methodName:compute.firewalls.patch OR protoPayload.methodName:compute.firewalls.delete)"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}



##### Network Change

resource "google_logging_metric" "network_metric" {
  for_each = var.create_network_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_network_logging_metrics"
  filter   = "resource.type=gce_network AND (protoPayload.methodName:compute.networks.insert OR protoPayload.methodName:compute.networks.patch OR protoPayload.methodName:compute.networks.delete OR protoPayload.methodName:compute.networks.removePeering OR protoPayload.methodName:compute.networks.addPeering)"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}



##### Route Change

resource "google_logging_metric" "route_metric" {
  for_each = var.create_route_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_route_logging_metrics"
  filter   = "resource.type=gce_route AND (protoPayload.methodName:compute.routes.delete OR protoPayload.methodName:compute.routes.insert)"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}



##### IAM Change

resource "google_logging_metric" "iam_metric" {
  for_each = var.create_iam_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_iam_logging_metrics"
  filter   = "protoPayload.methodName=SetIamPolicy AND protoPayload.serviceData.policyDelta.auditConfigDeltas:*"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}



##### Custom Role 

resource "google_logging_metric" "custom_role_metric" {
  for_each = var.create_custom_role_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_custom_role_logging_metrics"
  filter   = "resource.type=iam_role AND (protoPayload.methodName=google.iam.admin.v1.CreateRole OR protoPayload.methodName=google.iam.admin.v1.DeleteRole OR protoPayload.methodName=google.iam.admin.v1.UpdateRole)"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}



##### Project Owner Role Binding

resource "google_logging_metric" "owner_role_metric" {
  for_each = var.create_owner_role_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_owner_role_logging_metrics"
  filter   = "(protoPayload.serviceName=cloudresourcemanager.googleapis.com) AND (ProjectOwnership OR projectOwnerInvitee) OR (protoPayload.serviceData.policyDelta.bindingDeltas.action=REMOVE AND protoPayload.serviceData.policyDelta.bindingDeltas.role=roles/owner) OR (protoPayload.serviceData.policyDelta.bindingDeltas.action=ADD AND protoPayload.serviceData.policyDelta.bindingDeltas.role=roles/owner)"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}

##### Bucket Level IAM binding

resource "google_logging_metric" "bucket_iam_metric" {
  for_each = var.create_bucket_iam_metric ? toset(var.project_ids) : []
  project  = each.value
  name     = "${each.value}_bucket_iam_logging_metrics"
  filter   = "resource.type=gcs_bucket AND protoPayload.methodName=storage.setIamPermissions"

  bucket_name = "projects/${each.value}/locations/global/buckets/_Default"

  metric_descriptor {
    metric_kind = "DELTA"
    value_type  = "INT64"
    unit        = "1"
  }
}