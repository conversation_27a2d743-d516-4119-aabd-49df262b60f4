#########################
#### Create Tags Key ####
#########################

variable "tag_for" {
  type        = string
  description = "Tags Created for organization or project"
  default     = "organization"
}

variable "org_id" {
  type        = string
  description = "Organization ID"
  default     = ""
}

variable "project_number" {
  type        = string
  description = "Project Number"
  default     = ""
}

variable "key" {
  description = "Key for Tags"
  type        = string
}

variable "key_description" {
  type        = string
  description = "Description for the Key"
  default     = ""
}

variable "key_purpose" {
  type        = string
  description = "Purpose specification for the Key"
  default     = ""
}

variable "key_purpose_data" {
  type        = map(string)
  description = "Purpose Data specification for the key"
  default     = {}
}

####################################
#### Create Tags Value for keys ####
####################################

variable "value_specs" {
  type = list(object({
    value       = string
    description = string
    tag_binding = map(list(string))
  }))
  description = "Value specifications"
  default     = []
}