#########################
#### Create Tags Key ####
#########################

resource "google_tags_tag_key" "key" {
  parent       = var.tag_for == "organization" ? "organizations/${var.org_id}" : var.tag_for == "project" ? "projects/${var.project_number}" : null
  short_name   = var.key
  description  = var.key_description
  purpose      = var.key_purpose
  purpose_data = var.key_purpose_data
}

####################################
#### Create Tags Value for keys ####
####################################

resource "google_tags_tag_value" "value" {
  for_each    = { for val_desc in var.value_specs : val_desc.value => val_desc }
  parent      = "tagKeys/${google_tags_tag_key.key.name}"
  short_name  = each.key
  description = each.value.description
  depends_on  = [google_tags_tag_key.key]
}

######################################
#### Create Tags Binding Globally ####
######################################

resource "google_tags_tag_binding" "binding" {
  for_each = {
    for idx, item in flatten([
      for val_idx, val_desc in var.value_specs : [
        for tag_region, tag_binding in val_desc.tag_binding :
        tag_region == "global" ? [
          for bind_idx, bind in tag_binding : {
            key    = "${val_idx}-${bind_idx}"
            value  = val_desc.value
            parent = bind
          }
        ] : []
      ]
    ]) : item.key => item
  }
  parent    = each.value.parent
  tag_value = "tagValues/${google_tags_tag_value.value[each.value.value].name}"
}

##################################################
#### Create Tags Binding specific to Location ####
##################################################

resource "google_tags_location_tag_binding" "binding" {
  for_each = {
    for idx, item in flatten([
      for val_idx, val_desc in var.value_specs : [
        for tag_region, tag_binding in val_desc.tag_binding :
        tag_region != "global" ? [
          for bind_idx, bind in tag_binding : {
            key      = "${val_idx}-${tag_region}-${bind_idx}"
            value    = val_desc.value
            location = tag_region
            parent   = bind
          }
        ] : []
      ]
    ]) : item.key => item
  }
  parent    = each.value.parent
  tag_value = "tagValues/${google_tags_tag_value.value[each.value.value].name}"
  location  = each.value.location
}
