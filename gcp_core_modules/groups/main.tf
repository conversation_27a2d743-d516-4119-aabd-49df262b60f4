

module "saascada-groups" {
  for_each     = { for group in var.user_groups : group.group_name => group }
  source       = "terraform-google-modules/group/google"
  version      = "~> 0.7"
  id           = "${each.value.group_name}@saascada.com"
  display_name = each.value.group_name
  description  = "Groups creation for Saascada"
  customer_id  = var.customer_id

  members = each.value.group_members
}



