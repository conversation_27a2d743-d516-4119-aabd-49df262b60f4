###########################
####### GCP Project #######
###########################

resource "google_project" "project" {
  name                = coalesce(var.project_id, "prj-${var.client_prefix}-${var.environment}")
  project_id          = coalesce(var.project_id, "prj-${var.client_prefix}-${var.environment}")
  org_id              = var.org_id
  folder_id           = var.folder_id
  billing_account     = var.billing_account
  deletion_policy     = var.deletion_policy
  auto_create_network = var.auto_create_network
  labels              = var.labels
}

############################
###### Enabling APIs #######
############################

resource "google_project_service" "enable_apis" {
  project                    = google_project.project.project_id
  for_each                   = toset(var.enable_apis) != null ? toset(var.enable_apis) : toset([])
  service                    = each.value
  disable_on_destroy         = var.disable_on_destroy
  disable_dependent_services = var.disable_dependent_services

  depends_on = [
    google_project.project
  ]
}

################################################
##### Deprivilege default service accounts #####
################################################

resource "google_project_default_service_accounts" "project" {
  count   = var.deprivilege_sa ? 1 : 0
  project = google_project.project.project_id
  action  = "DEPRIVILEGE"

  depends_on = [
    google_project.project
  ]
}

###########################################################
##### New Service Account Creation for Compute Engine #####
###########################################################

resource "google_service_account" "sa" {
  count        = var.create_sa ? 1 : 0
  project      = google_project.project.project_id
  account_id   = "sa-${var.client_prefix}-${var.environment}-${var.region_prefix}-compute"
  display_name = "Compute Engine Service Account"
}