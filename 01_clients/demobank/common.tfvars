org_id          = "organizations/************"
client_prefix   = "demobank"
app_owner       = "demobank-owner"
billing_account = "01696D-176235-14EB50"
region          = "europe-west2"
region_prefix   = "ew2"

armor_config = {
  "sql_injection"                  = { enabled = true, priority = 1000 }
  "cross_site_scripting"           = { enabled = true, priority = 1001 }
  "local_file_inclusion"           = { enabled = false, priority = 1002 }
  "remote_file_inclusion"          = { enabled = false, priority = 1003 }
  "remote_code_execution"          = { enabled = true, priority = 1004 }
  "method_enforcement"             = { enabled = false, priority = 1005 }
  "scanner_detection"              = { enabled = false, priority = 1006 }
  "protocol_attack"                = { enabled = false, priority = 1007 }
  "php_injection_attack"           = { enabled = false, priority = 1008 }
  "session_fixation_attack"        = { enabled = false, priority = 1009 }
  "java_attack"                    = { enabled = false, priority = 1010 }
  "nodejs_attack"                  = { enabled = true, priority = 1011 }
  "new_discovered_vulnerabilities" = { enabled = true, priority = 900 }
}

alert_config = [
  {
    create_iam_metric           = true
    create_custom_role_metric   = true
    create_owner_role_metric    = true
    create_bucket_iam_metric    = true
    create_gce_alert            = false
    create_sql_alert            = true
    create_pubsub_alert         = true
    create_cloud_run_alert      = true
    create_cloud_function_alert = true
    create_firewall_metric      = true
    create_network_metric       = true
    create_route_metric         = true
    create_gke_alert            = false
    create_redis_alert          = true
  }
]





