# Ignore Terraform provider binaries and state files
.terraform/
*.tfstate
*.tfstate.backup

# Ignore Terraform crash log files
crash.log

# Ignore override files that could be used to customize Terraform locally
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Ignore the Terraform plan binary output (useful for local debugging)
*.tfplan

# Ignore macOS .DS_Store files
.DS_Store

# Ignore Terraform lock file
.terraform.lock.hcl
*.terraform.lock.hcl

*tfplan*