
#########################################################
######################## Projects #######################
#########################################################

module "shrd_ops_project" {
  source          = "../../gcp_core_modules/project"
  project_id      = "prj-ops-shrd"
  billing_account = var.billing_account
  folder_id       = module.second_level_folders["shared"].folder_id
  create_sa       = false
  enable_apis = [
    "iam.googleapis.com",
    "stackdriver.googleapis.com",
    "cloudresourcemanager.googleapis.com",
  ]
}

module "shrd_billing_project" {
  source          = "../../gcp_core_modules/project"
  project_id      = "prj-billing-shrd"
  billing_account = var.billing_account
  folder_id       = module.second_level_folders["shared"].folder_id
  create_sa       = false
  enable_apis = [
    "iam.googleapis.com",
    "stackdriver.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "bigquery.googleapis.com",
    "cloudbilling.googleapis.com",
  ]
}
