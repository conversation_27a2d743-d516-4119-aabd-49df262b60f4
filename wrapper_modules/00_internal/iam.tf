###########################################################
################## Groups ###########################
###########################################################

module "saascada_groups" {
  source      = "../../gcp_core_modules/groups"
  customer_id = "C00uk5xog"
  user_groups = [
    {
      group_name    = "gcp-org-admins"
      group_members = ["<EMAIL>", "<EMAIL>"]
    },
    {
      group_name    = "gcp-org-viewers"
      group_members = ["<EMAIL>", "<EMAIL>"]
    },
    {
      group_name = "gcp-infra-admins"
      group_members = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
      ]
    },
    {
      group_name                   = "gcp-developers"
      group_members = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
      ]
    }
  ]
}



###########################################################################
############################# IAM Access at ORG level ######################
###########################################################################


module "org-viewer-iam-bindings" {
  source  = "terraform-google-modules/iam/google//modules/organizations_iam"
  version = "~> 8.0"

  organizations = [split("/", var.org_id)[1]]
  mode          = "additive"

  bindings = {
    "roles/resourcemanager.organizationViewer" = [
      "group:<EMAIL>"
    ],
    "roles/resourcemanager.folderViewer" = [
      "group:<EMAIL>"

    ],
    "roles/browser" = [
      "group:<EMAIL>"

    ],
    "roles/serviceusage.serviceUsageViewer" = [
      "group:<EMAIL>"
    ]
  }
}




module "org-admin-iam-bindings" {
  source  = "terraform-google-modules/iam/google//modules/organizations_iam"
  version = "~> 8.0"

  organizations = [split("/", var.org_id)[1]]
  mode          = "additive"

  bindings = {
    "roles/resourcemanager.organizationAdmin" = [
      "group:<EMAIL>"
    ],
    "roles/resourcemanager.folderAdmin" = [
      "group:<EMAIL>"

    ],
    "roles/resourcemanager.projectCreator" = [
      "group:<EMAIL>"

    ],
    "roles/iam.organizationRoleAdmin" = [
      "group:<EMAIL>"

    ],
    "roles/cloudsupport.techSupportEditor" = [
      "group:<EMAIL>"

    ],
    "roles/serviceusage.serviceUsageAdmin" = [
      "group:<EMAIL>"

    ],
    "roles/essentialcontacts.admin" = [
      "group:<EMAIL>"

    ],
    "roles/billing.user" = [
      "group:<EMAIL>"

    ]

  }

}


###########################################################################
########################## IAM Access at folder level #####################
###########################################################################



module "shared-editor-iam-bindings" {
  source  = "terraform-google-modules/iam/google//modules/folders_iam"
  version = "~> 8.0"

  folders = [
    module.second_level_folders["development"].folder_id,
    module.second_level_folders["simulator"].folder_id,
    module.second_level_folders["qa"].folder_id,
    module.second_level_folders["staging"].folder_id
  ]
  mode = "additive"

  bindings = {
    "roles/editor" = [
      "group:<EMAIL>"
    ]
  }

}

module "shared-viewer-iam-bindings" {
  source  = "terraform-google-modules/iam/google//modules/folders_iam"
  version = "~> 8.0"

  folders = [
    module.second_level_folders["production"].folder_id,
    module.second_level_folders["shared"].folder_id
  ]

  mode = "additive"

  bindings = {
    "roles/viewer" = [
      "group:<EMAIL>"
    ]
  }

}

