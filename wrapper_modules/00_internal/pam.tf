###########################################################################
########################## JIT Access at folder level #####################
###########################################################################

module "pam_access_shared_folder" {
  source = "../../gcp_core_modules/pam"
  pam_access = [{
    entitlement_id             = "pe-admins-shrd-write"
    parent_id                  = split("/", module.second_level_folders["shared"].folder_id)[1]
    parent_type                = "folder"
    entitlement_requesters     = ["group:<EMAIL>"]
    entitlement_approvers      = ["user:<EMAIL>"]
    max_request_duration_hours = 4
  }]
  organization_id = split("/", var.org_id)[1]
  role_bindings = [
    {
      role = "roles/writer"
    }
  ]
}
