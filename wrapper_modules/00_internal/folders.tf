###################################
####### First Level Folders #######
###################################

module "first_level_folder" {
  source       = "../../gcp_core_modules/folder"
  for_each     = toset(var.first_level_folders)
  display_name = each.value
  parent       = var.org_id
}

#########################################################
###### Second level Folders under Folder - testbank ######
#########################################################

module "second_level_folders" {
  source       = "../../gcp_core_modules/folder"
  for_each     = toset(var.second_level_folders)
  display_name = each.value
  parent       = module.first_level_folder["saascada"].folder_id
  depends_on   = [module.first_level_folder]
}