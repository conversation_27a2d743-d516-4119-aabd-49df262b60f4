variable "first_level_folders" {
  description = "Map of first-level folders to their second-level folders"
  type        = list(string)
}

variable "second_level_folders" {
  description = "values of second level folders"
  type        = list(string)
}

variable "org_id" {
  type        = string
  description = "organization id"
}

variable "organization_id" {
  type        = string
  default     = ""
  description = "organization ID"
}

variable "billing_account" {
  type        = string
  description = "The alphanumeric ID of the billing account this project belongs to"
  default     = "01696D-176235-14EB50"
}

variable "project_id" {
  type        = string
  description = "project ID of the project"
  default     = ""
}

variable "region" {
  type        = string
  description = "region where the biq query dataset will get created for billing export"
  default     = "europe-west2"
}

variable "notification_channels" {
  type = map(object({
    display_name  = string
    email_address = string
  }))
  description = "List of notification channels"
  default     = {}
}
