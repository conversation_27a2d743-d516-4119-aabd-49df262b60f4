###################################
####### Tags and Tag Binding ######
###################################

module "tags" {
  source          = "../../gcp_core_modules/tags"
  tag_for         = "organization"
  org_id          = split("/", var.org_id)[1]
  key             = "app"
  key_description = "Application Name"
  value_specs = [
    {
      value       = "saascada"
      description = "Saascada Application"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.first_level_folder["saascada"].folder_id}"],
      }
    },
    {
      value       = "syscadaa"
      description = "System Application"
      tag_binding = {
        # "global" : ["//cloudresourcemanager.googleapis.com/${module.first_level_folder["saascada"].folder_id}"],
      }
    }
  ]
}


module "tags_environment" {
  source          = "../../gcp_core_modules/tags"
  tag_for         = "organization"
  org_id          = split("/", var.org_id)[1]
  key             = "environment"
  key_description = "A value that identifies the type of environment, such as dev, prod etc"
  value_specs = [
    {
      value       = "dev"
      description = "Development Environment"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.second_level_folders["development"].folder_id}"],
      }
    },
    {
      value       = "prod"
      description = "Production Environment"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.second_level_folders["production"].folder_id}"],
      }
    },
    {
      value       = "stg"
      description = "Staging Environment"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.second_level_folders["staging"].folder_id}"],
      }
    },
    {
      value       = "sim"
      description = "Simulation Environment"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.second_level_folders["simulator"].folder_id}"],
      }
    },
    {
      value       = "qa"
      description = "QA Environment"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.second_level_folders["qa"].folder_id}"],
      }
    },
    {
      value       = "shrd"
      description = "Shared Environment"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/${module.second_level_folders["shared"].folder_id}"],
      }
    }
  ]
}