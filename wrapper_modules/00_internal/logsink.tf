
###################################
############ Log Sink #############
###################################

locals {
  logging_sink_filter = <<EOF
     logName: /logs/cloudaudit.googleapis.com%2Factivity OR
     logName: /logs/cloudaudit.googleapis.com%2Fsystem_event OR
     logName: /logs/cloudaudit.googleapis.com%2Fdata_access OR
     logName: /logs/compute.googleapis.com%2Fpolicy OR
     logName: /logs/cloudaudit.googleapis.com%2Faccess_transparency
 EOF
}

###################################
########## Dev Log Sink ###########
###################################


module "dev_fldr_logs_export" {
  source = "../../gcp_core_modules/logsink"

  resources = {
    parent_id = module.second_level_folders["development"].folder_id
  }

  resource_type                  = "folder"
  logging_destination_project_id = module.shrd_ops_project.project_id
  logbucket_options = {
    logging_sink_name   = "logsink-dev-fldr"
    logging_sink_filter = local.logging_sink_filter
    name                = "logbkt-dev-fldr"
  }
}

###################################
########### QA Log Sink ###########
###################################

module "qa_fldr_logs_export" {
  source = "../../gcp_core_modules/logsink"

  resources = {
    parent_id = module.second_level_folders["qa"].folder_id
  }

  resource_type                  = "folder"
  logging_destination_project_id = module.shrd_ops_project.project_id
  logbucket_options = {
    logging_sink_name   = "logsink-qa-fldr"
    logging_sink_filter = local.logging_sink_filter
    name                = "logbkt-qa-fldr"
  }
}


###################################
########## Sim Log Sink ###########
###################################

module "sim_fldr_logs_export" {
  source = "../../gcp_core_modules/logsink"

  resources = {
    parent_id = module.second_level_folders["simulator"].folder_id
  }

  resource_type                  = "folder"
  logging_destination_project_id = module.shrd_ops_project.project_id
  logbucket_options = {
    logging_sink_name   = "logsink-sim-fldr"
    logging_sink_filter = local.logging_sink_filter
    name                = "logbkt-sim-fldr"
  }
}

###################################
########## Stg Log Sink ###########
###################################

module "stg_fldr_logs_export" {
  source = "../../gcp_core_modules/logsink"

  resources = {
    parent_id = module.second_level_folders["staging"].folder_id
  }

  resource_type                  = "folder"
  logging_destination_project_id = module.shrd_ops_project.project_id
  logbucket_options = {
    logging_sink_name   = "logsink-stg-fldr"
    logging_sink_filter = local.logging_sink_filter
    name                = "logbkt-stg-fldr"
  }
}

###################################
########## Prod Log Sink ##########
###################################

module "prod_fldr_logs_export" {
  source = "../../gcp_core_modules/logsink"

  resources = {
    parent_id = module.second_level_folders["production"].folder_id
  }

  resource_type                  = "folder"
  logging_destination_project_id = module.shrd_ops_project.project_id
  logbucket_options = {
    logging_sink_name   = "logsink-prod-fldr"
    logging_sink_filter = local.logging_sink_filter
    name                = "logbkt-prod-fldr"
    retention_days      = "365"
    locked              = true
  }
}