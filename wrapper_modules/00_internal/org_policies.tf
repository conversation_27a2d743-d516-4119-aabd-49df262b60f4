#########################################################
######################## Org Policies #####################
#########################################################

module "org_policies" {
  source = "../../gcp_core_modules/org_policies"

  parent                                              = module.first_level_folder["saascada"].folder_id
  org_restrict_protocol_forwarding_creation_for_types = ["INTERNAL"]
  iam_domain_restricted_sharing                       = ["C00uk5xog", "C01ysash6"]
  iam_allowed_contact_domains                         = ["@saascada.com", "@searce.com"]
  org_vm_external_ip_access                           = []
  gcp_restrictResourceLocations                       = ["in:europe-west2-locations"]

  iam_disableServiceAccountKeyCreation            = true
  iam_disableServiceAccountKeyUpload              = true
  iam_automaticIamGrantsForDefaultServiceAccounts = true
  compute_disableVpcExternalIpv6                  = true
  compute_disableNestedVirtualization             = true
  sql_restrictPublicIp                            = true
  sql_restrictAuthorizedNetworks                  = true
  storage_bucket_publicAccessPrevention           = true
  storage_uniformBucketLevelAccess                = true
  compute_network_restrictXpnProjectLienRemoval   = true
  compute_network_skipDefaultNetworkCreation      = true
}
