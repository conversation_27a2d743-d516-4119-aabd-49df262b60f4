###################################
####### Tags and Tag Binding ######
###################################

module "tags_client" {
  source          = "../../gcp_core_modules/tags"
  tag_for         = "project"
  project_number  = module.project.project_number
  key             = "client"
  key_description = "Client Name"
  value_specs = [
    {
      value       = var.client_prefix
      description = "Client Name"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/projects/${module.project.project_number}"],
      }
    }
  ]
}

module "tags_owner" {
  source          = "../../gcp_core_modules/tags"
  tag_for         = "project"
  project_number  = module.project.project_number
  key             = "owner"
  key_description = "Owner Name"
  value_specs = [
    {
      value       = var.app_owner
      description = "Owner Name"
      tag_binding = {
        "global" : ["//cloudresourcemanager.googleapis.com/projects/${module.project.project_number}"],
      }
    }
  ]
}