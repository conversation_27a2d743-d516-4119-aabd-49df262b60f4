############################
##### Private DNS Zone #####
############################

module "peering_dns_zone_saascada" {
  source = "../../gcp_core_modules/cloud_dns_zone"

  for_each = toset(var.dns_zone_type)
  project_id  = module.project.project_id
  client_prefix = var.client_prefix
  environment = var.environment
  type = each.value
  description = "Peering DNS zone for saascada.io with a DNS peering with ${split("/", module.networking.self_link)[9]}"

  network_names = [
  module.networking.name
  ]
}
