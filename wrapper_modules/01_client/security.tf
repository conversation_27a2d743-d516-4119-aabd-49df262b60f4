##########################################################################
#########################  Firewall Rules    #############################
##########################################################################

module "security" {
  source        = "../../gcp_core_modules/security"
  client_prefix = var.client_prefix
  environment   = var.environment
  network_name  = module.networking.name
  project_id    = module.project.project_id
  rules = concat([
    {
      name               = "allow-iap"
      direction          = "INGRESS"
      source_ranges      = ["35.235.240.0/20"]
      destination_ranges = []
      priority           = 56000
      target_tags        = ["iap-ssh"]
      allow = [{
        protocol = "tcp"
        ports    = ["22"]
      }]
      deny       = []
      log_config = null
    },
    {
      name               = "allow-lb-hc"
      direction          = "INGRESS"
      source_ranges      = ["35.191.0.0/16", "130.211.0.0/22", "209.85.152.0/22", "209.85.204.0/22"]
      destination_ranges = []
      target_tags        = ["http-lb"]
      priority           = 57000
      allow = [{
        protocol = "tcp"
        ports    = ["80", "443"]
      }]
      deny       = []
      log_config = null
    }
  ], var.rules)
}



##########################################################################
######################### Cloud Armor Policy #############################
##########################################################################

module "cloud_armor_policy" {
  source        = "../../gcp_core_modules/cloud_armor"
  client_prefix = var.client_prefix
  environment   = var.environment
  project_id    = module.project.project_id
  #blocked_ips    = var.blocked_ips
  armor_config = var.armor_config
}