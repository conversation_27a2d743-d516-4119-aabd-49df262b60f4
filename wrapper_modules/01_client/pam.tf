###########################################################################
########################## JIT Access at project level #####################
###########################################################################

module "pam_access_dev_project" {
  source = "../../gcp_core_modules/pam"
  pam_access = [{
    entitlement_id             = "pe-developers-${var.client_prefix}-${var.environment}-write"
    parent_id                  = module.project.project_id
    parent_type                = "project"
    entitlement_requesters     = ["group:<EMAIL>"]
    entitlement_approvers      = ["user:<EMAIL>", "user:<EMAIL>"]
    max_request_duration_hours = 4
  }]
  organization_id = split("/", var.org_id)[1]
  role_bindings = [
    {
      role = "roles/writer"
    }
  ]
}


