
# Local to filter notification channels based on environment
locals {
  notification_channels = [var.environment == "prod" ? data.terraform_remote_state.internal_state.outputs.notification_channels["prod-channel"] : data.terraform_remote_state.internal_state.outputs.notification_channels["nonprod-channel"]]
}

module "alert-policies" {
  source                = "../../gcp_core_modules/monitoring_alerting"
  monitored_project_ids = [module.project.project_id]
  alerting_project_id   = "prj-ops-shrd"
  project_ids           = [module.project.project_id]
  notification_channels = local.notification_channels
  alert_config           = var.alert_config
}


