
###################################
########## Google Project #########
###################################

variable "org_id" {
  type        = string
  default     = ""
  description = "organization ID"
}

variable "environment" {
  type        = string
  description = "Environment name"
  default     = "dev"
}

variable "client_prefix" {
  type        = string
  description = "Client prefix"
  default     = "testbank"
}

variable "billing_account" {
  type        = string
  description = "The alphanumeric ID of the billing account this project belongs to"
  default     = "01696D-176235-14EB50"
}

variable "region_prefix" {
  type        = string
  description = "Prefix for region"
  default     = "ew2"
}

variable "enable_apis" {
  type        = list(string)
  description = "List of APIs that needs to be enabled on the project"
  default = [
    "compute.googleapis.com",
    "iam.googleapis.com",
    "stackdriver.googleapis.com",
    "iamcredentials.googleapis.com",
    "dns.googleapis.com",
    "servicenetworking.googleapis.com",
    "networkmanagement.googleapis.com",
    "container.googleapis.com",
    "iap.googleapis.com",
    "cloudresourcemanager.googleapis.com",
    "dataflow.googleapis.com",
    "secretmanager.googleapis.com",
    "privilegedaccessmanager.googleapis.com"
  ]
}

###################################
######## Google Networking ########
###################################


variable "region" {
  type        = string
  default     = "europe-west2"
  description = "Region for the deployment of resources"
}


###################################
######### Google Security #########
###################################

variable "rules" {
  type = list(object({
    name                    = string
    description             = optional(string)
    direction               = string
    priority                = number
    source_ranges           = list(string)
    destination_ranges      = list(string)
    source_tags             = optional(list(string), null)
    source_service_accounts = optional(list(string), null)
    target_tags             = optional(list(string), null)
    target_service_accounts = optional(list(string), null)
    allow = list(object({
      protocol = string
      ports    = list(string)
    }))
    deny = list(object({
      protocol = string
      ports    = list(string)
    }))
    log_config = optional(object({
      metadata = string
    }), null)
  }))
  description = "Firewall rules"
}

# variable "blocked_ips" {
#   description = "IP addresses or CIDR ranges to block."
#   type        = list(string)
#   default     = []
# }

variable "armor_config" {
  description = " To enable/disable armor rules and set priority"
  type = map(object({
    enabled  = bool
    priority = number
  }))
}

variable "subnet_config" {
  type = map(object({
    subnet_purpose     = string
    primary_cidr_range = string
  }))
}

variable "parent_folder" {
  type = string
}

variable "app_owner" {
  type = string
}

variable "psa_cidr" {
  type = string
  description = "CIDR range for PSA"
}

variable "alert_config" {
  type = list(object({
    create_iam_metric           = optional(bool, false)
    create_custom_role_metric   = optional(bool, false) 
    create_owner_role_metric    = optional(bool, false)
    create_bucket_iam_metric    = optional(bool, false) 
    create_gce_alert            = optional(bool, false)
    create_sql_alert            = optional(bool, false)
    create_pubsub_alert         = optional(bool, false) 
    create_gke_alert            = optional(bool, false)
    create_redis_alert          = optional(bool, false) 
    create_cloud_run_alert      = optional(bool, false) 
    create_cloud_function_alert = optional(bool, false) 
    create_firewall_metric      = optional(bool, false) 
    create_network_metric       = optional(bool, false) 
    create_route_metric         = optional(bool, false) 
  }))
  description = "Alerting configuration"
}


variable "dns_zone_type" {
  type = list(string)
  description = "public & private DNS zone types"
}